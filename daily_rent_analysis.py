#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
from datetime import datetime
import pandas as pd

def parse_date(date_str):
    """解析日期字符串"""
    if not date_str or date_str.strip() == '':
        return None
    try:
        # 尝试解析不同的日期格式
        if ' ' in date_str:
            return datetime.strptime(date_str.split()[0], '%Y-%m-%d')
        else:
            return datetime.strptime(date_str, '%Y-%m-%d')
    except:
        return None

def calculate_rental_days(start_date, end_date):
    """计算租赁天数"""
    if not start_date or not end_date:
        return 0
    try:
        start = parse_date(start_date)
        end = parse_date(end_date)
        if start and end:
            return (end - start).days + 1  # 包含开始和结束日期
        return 0
    except:
        return 0

def analyze_all_rentals():
    """分析所有订单的日租金"""
    
    # 读取数据
    rentals = []
    with open('rentals.txt', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f, delimiter='\t')
        for row in reader:
            rentals.append(row)
    
    print(f"总共找到 {len(rentals)} 条租赁记录\n")
    
    # 分析每个订单
    analysis_results = []
    
    for rental in rentals:
        order_id = rental['id']
        device = rental['device_number']
        customer = rental['customer_name']
        user_id = rental['user_id']
        rent_amount = float(rental['rent_amount']) if rental['rent_amount'] else 0
        
        # 计算租赁天数
        rental_days = calculate_rental_days(rental['start_date'], rental['end_date'])
        
        # 计算日均租金
        daily_rate = rent_amount / rental_days if rental_days > 0 else 0
        
        analysis_results.append({
            'order_id': order_id,
            'user_id': user_id,
            'device': device,
            'customer': customer,
            'rent_amount': rent_amount,
            'rental_days': rental_days,
            'daily_rate': daily_rate,
            'start_date': rental['start_date'],
            'end_date': rental['end_date'],
            'status': rental['send_status'],
            'note': rental['note']
        })
    
    # 按日租金排序（从高到低）
    analysis_results.sort(key=lambda x: x['daily_rate'], reverse=True)
    
    # 输出结果
    print("=" * 120)
    print("所有订单按日租金排序（从高到低）")
    print("=" * 120)
    print(f"{'排名':<4} {'订单ID':<6} {'用户ID':<6} {'设备':<8} {'客户':<12} {'总租金':<6} {'租期':<4} {'日租金':<8} {'状态':<8} {'备注':<25}")
    print("-" * 120)
    
    for i, result in enumerate(analysis_results, 1):
        print(f"{i:<4} {result['order_id']:<6} {result['user_id']:<6} {result['device']:<8} {result['customer']:<12} "
              f"{result['rent_amount']:<6.0f} {result['rental_days']:<4} {result['daily_rate']:<8.1f} "
              f"{result['status']:<8} {result['note'][:25]:<25}")
    
    # 统计分析
    print("\n" + "=" * 120)
    print("统计分析：")
    print("=" * 120)
    
    # 过滤掉异常数据（租期<=0或租金=0）
    valid_results = [r for r in analysis_results if r['rental_days'] > 0 and r['rent_amount'] > 0]
    
    if valid_results:
        daily_rates = [r['daily_rate'] for r in valid_results]
        
        print(f"有效订单数量: {len(valid_results)}")
        print(f"最高日租金: {max(daily_rates):.1f}元/天")
        print(f"最低日租金: {min(daily_rates):.1f}元/天")
        print(f"平均日租金: {sum(daily_rates)/len(daily_rates):.1f}元/天")
        
        # 按日租金区间统计
        ranges = [
            (0, 10, "0-10元"),
            (10, 20, "10-20元"),
            (20, 30, "20-30元"),
            (30, 40, "30-40元"),
            (40, 50, "40-50元"),
            (50, float('inf'), "50元以上")
        ]
        
        print(f"\n日租金区间分布:")
        for min_val, max_val, label in ranges:
            count = len([r for r in valid_results if min_val <= r['daily_rate'] < max_val])
            percentage = count / len(valid_results) * 100
            print(f"{label}: {count}单 ({percentage:.1f}%)")
    
    # 按用户统计
    print(f"\n按用户统计:")
    user_stats = {}
    for result in valid_results:
        user_id = result['user_id']
        if user_id not in user_stats:
            user_stats[user_id] = {'count': 0, 'total_amount': 0, 'total_days': 0}
        user_stats[user_id]['count'] += 1
        user_stats[user_id]['total_amount'] += result['rent_amount']
        user_stats[user_id]['total_days'] += result['rental_days']
    
    for user_id, stats in user_stats.items():
        avg_daily_rate = stats['total_amount'] / stats['total_days'] if stats['total_days'] > 0 else 0
        print(f"用户{user_id}: {stats['count']}单, 平均日租金: {avg_daily_rate:.1f}元/天")

if __name__ == "__main__":
    analyze_all_rentals()
